<script setup lang="tsx">
import { N<PERSON><PERSON>on, NPopconfirm, NTag } from 'naive-ui';
import type { DataTableSortState } from 'naive-ui';
import { jobApi } from '@/service/api';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { useDict } from '@/hooks/business/dict';
import SwitchStatus from '@/components/custom/switch-status.vue';
import OperateDrawer from './modules/OperateDrawer.vue';
import SearchBar from './modules/SearchBar.vue';

const appStore = useAppStore();

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  pagination,
  searchParams,
  resetSearchParams,
  updateSearchParams
} = useTable({
  apiFn: jobApi.list,
  showTotal: true,
  apiParams: {
    _page: 1,
    _limit: 10,
    _sort: 'order,id',
    _order: 'desc,desc',
    _expand: 'company',
    status: null,
    title: null,
    companyId: null,
    flag: null
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'title',
      title: '岗位名称',
      align: 'left'
    },
    {
      key: 'companyId',
      title: '招聘单位',
      align: 'left',
      ellipsis: true,
      render: row => row.company?.name || '-'
    },
    {
      key: 'type',
      title: '岗位类型',
      align: 'left',
      render: row => {
        if (row.type === null) return null;

        const option = useDict('number').item('JobType', row.type);
        if (!option) return null;
        return <NTag type={option.type}>{option.label}</NTag>;
      }
    },
    {
      key: 'salary',
      title: '薪资',
      align: 'center',
      render: row => {
        if (row.salaryType === 1 && row.salary) {
          return (
            <NTag type="success">
              {row.salary[0] / 1000}~{row.salary[1] / 1000} K
            </NTag>
          );
        }
        return <NTag type="success">面议</NTag>;
      }
    },
    {
      key: 'contact',
      title: '联系人',
      align: 'left'
    },
    {
      key: 'phone',
      title: '电话',
      align: 'left'
    },
    {
      key: 'flag',
      title: '标志',
      align: 'left',
      render: row => (
        <div class="flex flex-wrap items-center gap-8px">
          {row.flag.map(val => {
            const option = useDict('number').item('Flag', val);
            return (
              <NTag key={val} type={option?.type}>
                {option?.label}
              </NTag>
            );
          })}
        </div>
      )
    },
    {
      key: 'order',
      title: '排序',
      align: 'left',
      sorter: true
    },
    {
      key: 'status',
      title: '状态',
      align: 'center',
      render: row => <SwitchStatus v-model:value={row.status} apiFn={status => jobApi.save({ ...row, status })} />
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 150,
      render: row => (
        <div class="flex flex-center gap-12px">
          <NButton type="primary" text size="small" onClick={() => edit(row.id)}>
            编辑
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => '确定删除吗？',
              trigger: () => (
                <NButton type="error" text size="small">
                  删除
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys, onBatchDeleted, onDeleted } =
  useTableOperate(data, getData);

async function handleBatchDelete() {
  const { error } = await jobApi.batchDel(checkedRowKeys.value);

  if (!error) {
    onBatchDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

async function handleDelete(id: number) {
  const { error } = await jobApi.del(id);

  if (!error) {
    onDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

async function handleSort(sorter: DataTableSortState) {
  if (sorter.order) {
    updateSearchParams({
      _sort: sorter.columnKey as string,
      _order: sorter.order === 'ascend' ? 'asc' : 'desc'
    });
  } else {
    updateSearchParams({
      _sort: 'order',
      _order: 'asc'
    });
  }
  await getData();
}

function edit(id: number) {
  handleEdit(id);
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-12px overflow-hidden lt-sm:overflow-auto">
    <SearchBar v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="1024"
        :loading="loading"
        remote
        striped
        :bordered="false"
        :row-key="row => row.id"
        :pagination="pagination"
        class="b-t-1px sm:h-full b-auto"
        @update:sorter="handleSort"
      />
      <OperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
