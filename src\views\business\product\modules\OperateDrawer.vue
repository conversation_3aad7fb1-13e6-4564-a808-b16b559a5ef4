<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { companyApi, productApi } from '@/service/api';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useDict } from '@/hooks/business/dict';
import WangEditor from '@/components/custom/wang-editor.vue';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Business.Product | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const loading = ref(false);

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增产品',
    edit: '编辑产品'
  };
  return titles[props.operateType];
});

type Model = Pick<
  Api.Business.Product,
  | 'name'
  | 'code'
  | 'type'
  | 'spec'
  | 'material'
  | 'standard'
  | 'surface'
  | 'strength'
  | 'cover'
  | 'detail'
  | 'albums'
  | 'tags'
  | 'companyId'
  | 'order'
  | 'flag'
  | 'status'
>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    name: '',
    code: '',
    type: null,
    spec: '',
    material: '',
    standard: '',
    surface: '',
    strength: '',
    cover: '',
    detail: '',
    albums: [],
    tags: [],
    companyId: null,
    order: 0,
    flag: [],
    status: true
  };
}

type RuleKey = Extract<keyof Model, 'name' | 'companyId' | 'code' | 'type'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  name: defaultRequiredRule,
  companyId: defaultRequiredRule,
  code: defaultRequiredRule,
  type: defaultRequiredRule
};

function handleInitModel() {
  model.value = createDefaultModel();

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model.value, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  loading.value = true;
  await validate();
  // request
  const { error } =
    props.operateType === 'edit'
      ? await productApi.save({ id: props.rowData!.id, ...model.value })
      : await productApi.add(model.value);
  loading.value = false;

  if (!error) {
    window.$message?.success(`${title.value}成功`);
    closeDrawer();
    emit('submitted');
  } else {
    window.$message?.error(`${title.value}失败`);
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="800">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" :label-width="100" label-placement="left">
        <NTabs type="card" placement="left" animated>
          <NTabPane name="base" tab="基础信息">
            <NFormItem label="所属公司" path="companyId">
              <RemoteSelect
                v-model:value="model.companyId"
                label-field="name"
                value-field="id"
                :options="[rowData?.company]"
                :api-fn="companyApi.list"
              />
            </NFormItem>
            <NGrid :cols="2" :x-gap="12">
              <NFormItemGi label="产品名称" path="name">
                <NInput v-model:value="model.name" placeholder="请输入名称" clearable />
              </NFormItemGi>
              <NFormItemGi label="编码" path="code">
                <NInput v-model:value="model.code" placeholder="请输入编码" clearable />
              </NFormItemGi>
              <NFormItemGi label="类型" path="type">
                <NSelect
                  v-model:value="model.type"
                  placeholder="请选择类型"
                  :options="useDict('number').items('ProductType')"
                />
              </NFormItemGi>
              <NFormItemGi label="规格" path="spec">
                <NInput v-model:value="model.spec" placeholder="请输入规格" clearable />
              </NFormItemGi>
              <NFormItemGi label="材质" path="material">
                <NInput v-model:value="model.material" placeholder="请输入材质" clearable />
              </NFormItemGi>
              <NFormItemGi label="标准" path="standard">
                <NInput v-model:value="model.standard" placeholder="请输入标准" clearable />
              </NFormItemGi>
              <NFormItemGi label="表面" path="surface">
                <NInput v-model:value="model.surface" placeholder="请输入表面" clearable />
              </NFormItemGi>
              <NFormItemGi label="强度" path="strength">
                <NInput v-model:value="model.strength" placeholder="请输入强度" clearable />
              </NFormItemGi>
            </NGrid>
            <NFormItem label="封面" path="cover">
              <UploadCover v-model:value="model.cover" :options="{ maxWidth: 800, maxHeight: 800 }" />
            </NFormItem>
            <NFormItem label="标签" path="tags">
              <NDynamicTags v-model:value="model.tags" />
            </NFormItem>
          </NTabPane>
          <NTabPane name="albums" tab="相册管理">
            <UploadFiles v-model:value="model.albums" accept="image/*" />
          </NTabPane>
          <NTabPane name="detail" tab="编辑详情">
            <WangEditor v-model:value="model.detail" />
          </NTabPane>
          <NTabPane name="more" tab="更多">
            <NFormItem label="标志" path="flag">
              <NSelect
                v-model:value="model.flag"
                placeholder="请选择标志"
                :options="useDict('number').items('Flag')"
                multiple
                clearable
              />
            </NFormItem>
            <NFormItem label="排序" path="order">
              <NInputNumber v-model:value="model.order" placeholder="请输入排序" clearable />
            </NFormItem>
            <NFormItem label="状态" path="status">
              <NSwitch v-model:value="model.status" />
            </NFormItem>
          </NTabPane>
        </NTabs>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">取消</NButton>
          <NButton type="primary" :loading="loading" @click="handleSubmit">确定</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
