/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { RouteComponent } from "vue-router";
import type { LastLevelRouteKey, RouteLayout } from "@elegant-router/types";

import BaseLayout from "@/layouts/base-layout/index.vue";
import BlankLayout from "@/layouts/blank-layout/index.vue";

export const layouts: Record<RouteLayout, RouteComponent | (() => Promise<RouteComponent>)> = {
  base: BaseLayout,
  blank: BlankLayout,
};

export const views: Record<LastLevelRouteKey, RouteComponent | (() => Promise<RouteComponent>)> = {
  403: () => import("@/views/_builtin/403/index.vue"),
  404: () => import("@/views/_builtin/404/index.vue"),
  500: () => import("@/views/_builtin/500/index.vue"),
  "iframe-page": () => import("@/views/_builtin/iframe-page/[url].vue"),
  login: () => import("@/views/_builtin/login/index.vue"),
  business_app: () => import("@/views/business/app/index.vue"),
  business_company: () => import("@/views/business/company/index.vue"),
  business_expert: () => import("@/views/business/expert/index.vue"),
  business_job: () => import("@/views/business/job/index.vue"),
  business_knowledge: () => import("@/views/business/knowledge/index.vue"),
  business_product: () => import("@/views/business/product/index.vue"),
  business_report: () => import("@/views/business/report/index.vue"),
  business_talent: () => import("@/views/business/talent/index.vue"),
  cms_meta: () => import("@/views/cms/meta/index.vue"),
  cms_post: () => import("@/views/cms/post/index.vue"),
  home: () => import("@/views/home/<USER>"),
  system_api: () => import("@/views/system/api/index.vue"),
  system_config: () => import("@/views/system/config/index.vue"),
  system_dept: () => import("@/views/system/dept/index.vue"),
  system_dict: () => import("@/views/system/dict/index.vue"),
  system_log: () => import("@/views/system/log/index.vue"),
  system_menu: () => import("@/views/system/menu/index.vue"),
  system_role: () => import("@/views/system/role/index.vue"),
  system_tenant: () => import("@/views/system/tenant/index.vue"),
  system_user: () => import("@/views/system/user/index.vue"),
  wms_dept: () => import("@/views/wms/dept/index.vue"),
  wms_user: () => import("@/views/wms/user/index.vue"),
};
