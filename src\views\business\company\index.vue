<script setup lang="tsx">
import { NButton, NImage, NPopconfirm, NTag } from 'naive-ui';
import type { DataTableSortState } from 'naive-ui';
import { companyApi } from '@/service/api';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { useDict } from '@/hooks/business/dict';
import SwitchStatus from '@/components/custom/switch-status.vue';
import OperateDrawer from './modules/OperateDrawer.vue';
import SearchBar from './modules/SearchBar.vue';

const appStore = useAppStore();

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  pagination,
  searchParams,
  resetSearchParams,
  updateSearchParams
} = useTable({
  apiFn: companyApi.list,
  showTotal: true,
  apiParams: {
    _page: 1,
    _limit: 10,
    _sort: 'order,id',
    _order: 'desc,desc',
    status: null,
    name: null,
    industry: null,
    type: null,
    flag: null
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'name',
      title: '名称',
      align: 'left',
      ellipsis: true
    },
    {
      key: 'logo',
      title: 'Logo',
      align: 'left',
      render: row =>
        row.logo ? (
          <NImage
            class="h-28px rd-8px"
            lazy
            src={row.logo.startsWith('http') ? row.logo : import.meta.env.VITE_IMAGE_BASE_URL + row.logo}
          />
        ) : (
          '-'
        )
    },
    {
      key: 'industry',
      title: '行业',
      align: 'left',
      render: row => {
        if (row.industry === null) return null;

        const option = useDict('number').item('Industry', row.industry);
        if (!option) return null;
        return <NTag type={option.type}>{option.label}</NTag>;
      }
    },
    {
      key: 'type',
      title: '类型',
      align: 'left',
      render: row => {
        if (row.type === null) return null;

        const option = useDict('number').item('CompanyType', row.type);
        if (!option) return null;
        return <NTag type={option.type}>{option.label}</NTag>;
      }
    },
    {
      key: 'size',
      title: '规模',
      align: 'left',
      render: row => {
        if (row.size === null) return null;

        const option = useDict('number').item('CompanySize', row.size);
        if (!option) return null;
        return <NTag type={option.type}>{option.label}</NTag>;
      }
    },
    {
      key: 'flag',
      title: '标志',
      align: 'left',
      render: row => (
        <div class="flex flex-wrap items-center gap-8px">
          {row.flag.map(val => {
            const option = useDict('number').item('Flag', val);
            return (
              <NTag key={val} type={option?.type}>
                {option?.label}
              </NTag>
            );
          })}
        </div>
      )
    },
    {
      key: 'order',
      title: '排序',
      align: 'left',
      sorter: true
    },
    {
      key: 'status',
      title: '状态',
      align: 'center',
      render: row => <SwitchStatus v-model:value={row.status} apiFn={status => companyApi.save({ ...row, status })} />
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 150,
      render: row => (
        <div class="flex flex-center gap-12px">
          <NButton type="primary" text size="small" onClick={() => edit(row.id)}>
            编辑
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => '确定删除吗？',
              trigger: () => (
                <NButton type="error" text size="small">
                  删除
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys, onBatchDeleted, onDeleted } =
  useTableOperate(data, getData);

async function handleBatchDelete() {
  const { error } = await companyApi.batchDel(checkedRowKeys.value);

  if (!error) {
    onBatchDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

async function handleDelete(id: number) {
  const { error } = await companyApi.del(id);

  if (!error) {
    onDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

async function handleSort(sorter: DataTableSortState) {
  if (sorter.order) {
    updateSearchParams({
      _sort: sorter.columnKey as string,
      _order: sorter.order === 'ascend' ? 'asc' : 'desc'
    });
  } else {
    updateSearchParams({
      _sort: 'order',
      _order: 'asc'
    });
  }
  await getData();
}

function edit(id: number) {
  handleEdit(id);
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-12px overflow-hidden lt-sm:overflow-auto">
    <SearchBar v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        striped
        :bordered="false"
        :row-key="row => row.id"
        :pagination="pagination"
        class="b-t-1px sm:h-full b-auto"
        @update:sorter="handleSort"
      />
      <OperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
