<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { appApi } from '@/service/api';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Business.App | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const loading = ref(false);

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增应用',
    edit: '编辑应用'
  };
  return titles[props.operateType];
});

type Model = Pick<Api.Business.App, 'name' | 'slug' | 'logo' | 'summary' | 'url' | 'tags' | 'order' | 'status'>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    name: '',
    slug: '',
    logo: '',
    summary: '',
    url: '',
    tags: [],
    order: 0,
    status: true
  };
}

type RuleKey = Extract<keyof Model, 'name' | 'slug'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  name: defaultRequiredRule,
  slug: defaultRequiredRule
};

function handleInitModel() {
  model.value = createDefaultModel();

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model.value, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  loading.value = true;
  await validate();
  // request
  const { error } = props.operateType === 'edit' ? await appApi.save(model.value) : await appApi.add(model.value);
  loading.value = false;

  if (!error) {
    window.$message?.success(`${title.value}成功`);
    closeDrawer();
    emit('submitted');
  } else {
    window.$message?.error(`${title.value}失败`);
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="500">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" :label-width="100" label-placement="left">
        <NFormItem label="名称" path="name">
          <NInput v-model:value="model.name" placeholder="请输入名称" clearable />
        </NFormItem>
        <NFormItem label="别名" path="slug">
          <NInput v-model:value="model.slug" placeholder="请输入别名" clearable />
        </NFormItem>
        <NFormItem label="图标" path="logo">
          <UploadCover v-model:value="model.logo" :options="{ maxWidth: 200, maxHeight: 200 }" />
        </NFormItem>
        <NFormItem label="描述" path="summary">
          <NInput v-model:value="model.summary" placeholder="请输入描述" type="textarea" clearable />
        </NFormItem>
        <NFormItem label="URL" path="url">
          <NInput v-model:value="model.url" placeholder="请输入URL" clearable />
        </NFormItem>
        <NFormItem label="标签" path="tags">
          <NDynamicTags v-model:value="model.tags" />
        </NFormItem>
        <NFormItem label="排序" path="order">
          <NInputNumber v-model:value="model.order" placeholder="请输入排序" clearable />
        </NFormItem>
        <NFormItem label="状态" path="status">
          <NSwitch v-model:value="model.status" />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">取消</NButton>
          <NButton type="primary" :loading="loading" @click="handleSubmit">确定</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
