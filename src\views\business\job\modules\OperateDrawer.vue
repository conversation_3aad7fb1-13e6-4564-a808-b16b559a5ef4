<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { useCascaderAreaData } from '@vant/area-data';
import { companyApi, jobApi } from '@/service/api';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useDict } from '@/hooks/business/dict';
import WangEditor from '@/components/custom/wang-editor.vue';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Business.Job | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const loading = ref(false);

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增职位',
    edit: '编辑职位'
  };
  return titles[props.operateType];
});

const areaData = useCascaderAreaData();

type Model = Pick<
  Api.Business.Job,
  | 'companyId'
  | 'title'
  | 'type'
  | 'salaryType'
  | 'salary'
  | 'tags'
  | 'detail'
  | 'area'
  | 'contact'
  | 'phone'
  | 'email'
  | 'order'
  | 'flag'
  | 'status'
>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    companyId: null,
    title: '',
    type: null,
    salaryType: 1,
    salary: [1500, 5000],
    tags: [],
    detail: '',
    area: null,
    contact: '',
    phone: '',
    email: '',
    order: 0,
    flag: [],
    status: true
  };
}

type RuleKey = Extract<keyof Model, 'companyId' | 'title' | 'type' | 'salaryType'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  companyId: defaultRequiredRule,
  title: defaultRequiredRule,
  type: defaultRequiredRule,
  salaryType: defaultRequiredRule
};

function handleInitModel() {
  model.value = createDefaultModel();

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model.value, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  loading.value = true;
  await validate();
  // request
  const { error } =
    props.operateType === 'edit'
      ? await jobApi.save({ id: props.rowData!.id, ...model.value })
      : await jobApi.add(model.value);
  loading.value = false;

  if (!error) {
    window.$message?.success(`${title.value}成功`);
    closeDrawer();
    emit('submitted');
  } else {
    window.$message?.error(`${title.value}失败`);
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="800">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" :label-width="100" label-placement="left">
        <NTabs type="card" placement="left" animated>
          <NTabPane name="base" tab="基础信息">
            <NGrid :cols="2" :x-gap="12">
              <NFormItemGi label="岗位名称" path="title">
                <NInput v-model:value="model.title" placeholder="请输入岗位名称" clearable />
              </NFormItemGi>
              <NFormItemGi label="招聘单位" path="companyId">
                <RemoteSelect
                  v-model:value="model.companyId"
                  label-field="name"
                  value-field="id"
                  :options="[rowData?.company]"
                  :api-fn="companyApi.list"
                />
              </NFormItemGi>
              <NFormItemGi label="薪资类型" path="salaryType">
                <NSelect
                  v-model:value="model.salaryType"
                  placeholder="请选择薪资类型"
                  :options="useDict('number').items('SalaryType')"
                />
              </NFormItemGi>
              <NFormItemGi label="岗位类型" path="type">
                <NSelect
                  v-model:value="model.type"
                  placeholder="请选择岗位类型"
                  :options="useDict('number').items('JobType')"
                />
              </NFormItemGi>
            </NGrid>
            <NFormItem v-if="model.salaryType === 1" label="薪资范围" path="salary">
              <NSlider v-model:value="model.salary" :min="1500" :max="50000" range :step="100" />
            </NFormItem>
            <NFormItem label="工作地点" path="area">
              <NCascader v-model:value="model.area" placeholder="请选择省市区" label-field="text" :options="areaData" />
            </NFormItem>
            <NFormItem label="标签" path="tags">
              <NDynamicTags v-model:value="model.tags" />
            </NFormItem>
          </NTabPane>
          <NTabPane name="detail" tab="编辑详情">
            <WangEditor v-model:value="model.detail" />
          </NTabPane>
          <NTabPane name="contact" tab="联系方式">
            <NFormItem label="联系人" path="contact">
              <NInput v-model:value="model.contact" placeholder="请输入联系人" clearable />
            </NFormItem>
            <NFormItem label="电话" path="phone">
              <NInput v-model:value="model.phone" placeholder="请输入电话" clearable />
            </NFormItem>
            <NFormItem label="邮箱" path="email">
              <NInput v-model:value="model.email" placeholder="请输入邮箱" clearable />
            </NFormItem>
          </NTabPane>
          <NTabPane name="more" tab="更多">
            <NFormItem label="标志" path="flag">
              <NSelect
                v-model:value="model.flag"
                placeholder="请选择标志"
                :options="useDict('number').items('Flag')"
                multiple
                clearable
              />
            </NFormItem>
            <NFormItem label="排序" path="order">
              <NInputNumber v-model:value="model.order" placeholder="请输入排序" clearable />
            </NFormItem>
            <NFormItem label="状态" path="status">
              <NSwitch v-model:value="model.status" />
            </NFormItem>
          </NTabPane>
        </NTabs>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">取消</NButton>
          <NButton type="primary" :loading="loading" @click="handleSubmit">确定</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
