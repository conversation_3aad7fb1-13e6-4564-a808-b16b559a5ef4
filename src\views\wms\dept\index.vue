<script setup lang="tsx">
import type { Ref } from 'vue';
import { ref, watch } from 'vue';
import { NButton, NPopconfirm } from 'naive-ui';
import { jsonClone } from '@sa/utils';
import { deptApi } from '@/service/api';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { convertToTree } from '@/utils/common';
import SwitchStatus from '@/components/custom/switch-status.vue';
import OperateDrawer from './modules/OperateDrawer.vue';
import SearchBar from './modules/SearchBar.vue';

const appStore = useAppStore();

const { columns, columnChecks, data, getData, getDataByPage, loading, searchParams, resetSearchParams, pagination } =
  useTable({
    apiFn: deptApi.list,
    showTotal: true,
    apiParams: {
      _page: 1,
      _limit: 10,
      _sort: 'order',
      _order: 'asc',
      status: null,
      name: null,
      code: null
    },
    columns: () => [
      {
        type: 'selection',
        align: 'center',
        width: 48
      },
      {
        key: 'name',
        title: '名称',
        align: 'left'
      },
      {
        key: 'code',
        title: '编码',
        align: 'left'
      },
      {
        key: 'summary',
        title: '描述',
        align: 'left',
        minWidth: 200,
        ellipsis: true
      },
      {
        key: 'status',
        title: '状态',
        align: 'center',
        render: row => <SwitchStatus v-model:value={row.status} apiFn={status => deptApi.save({ ...row, status })} />
      },
      {
        key: 'operate',
        title: '操作',
        align: 'center',
        fixed: 'right',
        width: 150,
        render: row => (
          <div class="flex flex-center gap-12px">
            <NButton type="primary" text size="small" onClick={() => addChild(row.id)}>
              新增
            </NButton>
            <NButton type="primary" text size="small" onClick={() => edit(row.id)}>
              编辑
            </NButton>
            <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
              {{
                default: () => '确定删除吗？',
                trigger: () => (
                  <NButton type="error" text size="small">
                    删除
                  </NButton>
                )
              }}
            </NPopconfirm>
          </div>
        )
      }
    ]
  });

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys, onBatchDeleted, onDeleted } =
  useTableOperate(data, getData);

// 树形数据
const treeData = ref<Api.System.Dept[]>([]);
watch(data, nval => {
  treeData.value = nval.length > 0 ? convertToTree(nval) : [];
});

async function handleBatchDelete() {
  const { error } = await deptApi.batchDel(checkedRowKeys.value);

  if (!error) {
    onBatchDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

async function handleDelete(id: number) {
  const { error } = await deptApi.del(id);

  if (!error) {
    onDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

function edit(id: number) {
  handleEdit(id);
}

function addChild(id: number) {
  (operateType as Ref<NaiveUI.TableOperateType | 'addChild'>).value = 'addChild';
  const findItem = data.value.find(item => item.id === id) || null;
  editingData.value = jsonClone(findItem);

  drawerVisible.value = true;
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-12px overflow-hidden lt-sm:overflow-auto">
    <SearchBar v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="treeData"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        striped
        :bordered="false"
        :row-key="row => row.id"
        class="b-t-1px sm:h-full b-auto"
      />
      <OperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        :tree-data="treeData"
        @submitted="getDataByPage"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
